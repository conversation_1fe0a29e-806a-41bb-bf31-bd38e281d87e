#!/usr/bin/env python3
"""
Test script for the optimized captcha solver
Validates performance and accuracy improvements
"""

import time
import sys
from ai_optimized import OptimizedCaptchaSolver

def test_solver_initialization():
    """Test solver initialization and configuration"""
    print("🧪 Testing solver initialization...")
    
    try:
        solver = OptimizedCaptchaSolver()
        
        # Verify configuration
        assert solver.complete_overlap_threshold == 5, "Complete overlap threshold should be 5"
        assert solver.close_object_threshold == 50, "Close object threshold should be 50"
        assert solver.api_timeout == 30, "API timeout should be 30 seconds"
        
        print("✅ Solver initialization test passed")
        return True
    except Exception as e:
        print(f"❌ Solver initialization test failed: {e}")
        return False

def test_screenshot_capture():
    """Test screenshot capture functionality"""
    print("🧪 Testing screenshot capture...")
    
    try:
        solver = OptimizedCaptchaSolver()
        
        # Test screenshot capture
        image_path = solver.capture_screen_area("test_capture.png")
        
        # Verify file was created
        import os
        assert os.path.exists(image_path), "Screenshot file should be created"
        
        # Verify file size (should be reasonable)
        file_size = os.path.getsize(image_path)
        assert file_size > 1000, "Screenshot file should have reasonable size"
        
        print("✅ Screenshot capture test passed")
        return True
    except Exception as e:
        print(f"❌ Screenshot capture test failed: {e}")
        return False

def test_performance_metrics():
    """Test performance improvements"""
    print("🧪 Testing performance metrics...")
    
    try:
        solver = OptimizedCaptchaSolver()
        
        # Measure screenshot capture time
        start_time = time.time()
        solver.capture_screen_area("perf_test.png")
        capture_time = time.time() - start_time
        
        # Screenshot should be fast (< 1 second)
        assert capture_time < 1.0, f"Screenshot capture should be fast, took {capture_time:.2f}s"
        
        print(f"✅ Performance test passed - Screenshot: {capture_time:.3f}s")
        return True
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        return False

def test_error_handling():
    """Test error handling and robustness"""
    print("🧪 Testing error handling...")
    
    try:
        solver = OptimizedCaptchaSolver()
        
        # Test with invalid image path
        result = solver.verify_complete_overlap("nonexistent.png", "obj1", "obj2")
        assert result is None, "Should return None for invalid image"
        
        print("✅ Error handling test passed")
        return True
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False

def test_coordinate_validation():
    """Test coordinate calculation and validation"""
    print("🧪 Testing coordinate validation...")
    
    try:
        solver = OptimizedCaptchaSolver()
        
        # Verify captcha bbox is valid
        bbox = solver.captcha_bbox
        assert len(bbox) == 4, "Captcha bbox should have 4 coordinates"
        assert bbox[2] > bbox[0], "Right should be greater than left"
        assert bbox[3] > bbox[1], "Bottom should be greater than top"
        
        # Verify slide button coordinates
        slide_coords = solver.slide_button_absolute
        assert len(slide_coords) == 2, "Slide button should have x,y coordinates"
        assert slide_coords[0] > 0 and slide_coords[1] > 0, "Coordinates should be positive"
        
        print("✅ Coordinate validation test passed")
        return True
    except Exception as e:
        print(f"❌ Coordinate validation test failed: {e}")
        return False

def run_comprehensive_test():
    """Run comprehensive test suite"""
    print("🚀 STARTING COMPREHENSIVE TEST SUITE")
    print("=" * 50)
    
    tests = [
        ("Solver Initialization", test_solver_initialization),
        ("Screenshot Capture", test_screenshot_capture),
        ("Performance Metrics", test_performance_metrics),
        ("Error Handling", test_error_handling),
        ("Coordinate Validation", test_coordinate_validation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} FAILED")
    
    print("\n" + "=" * 50)
    print(f"🎯 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ ALL TESTS PASSED - Optimized solver is ready!")
        return True
    else:
        print("❌ SOME TESTS FAILED - Please review and fix issues")
        return False

def demo_solver():
    """Demonstrate the optimized solver (requires manual positioning)"""
    print("\n🎮 DEMO MODE")
    print("=" * 30)
    print("Position your browser with a captcha and press Enter...")
    print("The solver will attempt to solve it in 3 seconds...")
    
    input("Press Enter when ready...")
    
    print("Starting in 3 seconds...")
    time.sleep(3)
    
    try:
        solver = OptimizedCaptchaSolver()
        success = solver.solve_captcha()
        
        if success:
            print("🎉 DEMO SUCCESS: Captcha solved!")
        else:
            print("⚠️ DEMO INCOMPLETE: Captcha solving failed")
            
    except Exception as e:
        print(f"❌ DEMO ERROR: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        demo_solver()
    else:
        run_comprehensive_test()
        
        # Ask if user wants to run demo
        print("\n" + "=" * 50)
        response = input("Would you like to run a live demo? (y/n): ").lower().strip()
        if response in ['y', 'yes']:
            demo_solver()
