import requests
import json
import base64
import pyautogu<PERSON>
from PIL import ImageGrab
import time

class OptimizedCaptchaSolver:
    def __init__(self):
        self.api_key = "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"
        self.api_url = "https://openrouter.ai/api/v1"
        
        # Captcha area coordinates (configurable for different screen resolutions)
        self.captcha_bbox = (1111, 611, 1525, 934)  # (left, top, right, bottom)
        self.slide_button_absolute = (1159, 891)  # Precise slide button coordinates
        
        # PERFORMANCE OPTIMIZATION SETTINGS
        self.complete_overlap_threshold = 5  # Maximum pixels for complete overlap
        self.close_object_threshold = 50     # Objects ≤50px apart use single drag
        self.api_timeout = 30                # API timeout in seconds
        
    def capture_screen_area(self, filename="captcha_temp.png"):
        """Optimized screenshot capture with minimal processing"""
        screenshot = ImageGrab.grab(bbox=self.captcha_bbox)
        screenshot.save(filename)
        return filename

    def analyze_with_vision(self, image_path):
        """Enhanced vision analysis with focus on complete overlap detection and adaptive strategy"""
        with open(image_path, "rb") as image_file:
            base64_image = base64.b64encode(image_file.read()).decode('utf-8')

        payload = {
            "model": "anthropic/claude-opus-4.1",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": """Analyze this slider captcha with EXTREME PRECISION for object detection and adaptive drag strategy.

CRITICAL OBJECT DETECTION REQUIREMENTS:
- Identify exactly TWO main objects in the captcha
- Calculate the precise CENTER coordinates of each object (pixel-perfect accuracy required)
- Measure the exact distance between object centers
- Determine object sizes for complete overlap calculation
- Objects must be detected with sub-pixel precision

DISTANCE AND STRATEGY ANALYSIS:
- Calculate exact pixel distance between object centers
- If distance ≤50px: recommend SINGLE_DRAG (continuous drag without stops)
- If distance >50px: recommend TWO_STEP_DRAG (60% then complete)
- CRITICAL: Complete overlap means source object center aligns with target center within 5 pixels

RESPONSE FORMAT (JSON only, no additional text):
{
  "objects": [
    {"name": "source_object", "coordinates": {"x": precise_x, "y": precise_y}, "size": {"width": w, "height": h}},
    {"name": "target_object", "coordinates": {"x": precise_x, "y": precise_y}, "size": {"width": w, "height": h}}
  ],
  "distance_analysis": {
    "pixel_distance": exact_distance_number,
    "drag_strategy": "SINGLE_DRAG|TWO_STEP_DRAG",
    "proximity_level": "CLOSE|FAR"
  },
  "move": {"from": "source_object", "to": "target_object"}
}"""
                        },
                        {
                            "type": "image_url",
                            "image_url": {"url": f"data:image/png;base64,{base64_image}"}
                        }
                    ]
                }
            ],
            "temperature": 0.05  # Lower temperature for more consistent results
        }

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        try:
            response = requests.post(f"{self.api_url}/chat/completions", 
                                   headers=headers, json=payload, timeout=self.api_timeout)
            
            if response.status_code != 200:
                return None

            response_json = response.json()
            return response_json

        except Exception as e:
            print(f"❌ Vision analysis error: {e}")
            return None

    def verify_complete_overlap(self, image_path, source_obj, target_obj):
        """Verify complete overlap using strict criteria - ACCURACY PRIORITY"""
        with open(image_path, "rb") as image_file:
            base64_image = base64.b64encode(image_file.read()).decode('utf-8')

        payload = {
            "model": "anthropic/claude-opus-4.1",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": f"""CRITICAL COMPLETE OVERLAP VERIFICATION for {source_obj} and {target_obj}.

STRICT COMPLETE OVERLAP CRITERIA (NO PARTIAL OVERLAP ACCEPTED):
- The {source_obj} center must align with {target_obj} center within 5 pixels maximum
- The {source_obj} must visually cover/hide the majority of the {target_obj}
- NO GAPS visible between the objects
- NO PARTIAL OVERLAP - only complete overlap is acceptable
- The {source_obj} should appear to be positioned directly on top of the {target_obj}

VERIFICATION REQUIREMENTS:
- Measure exact center-to-center distance between objects
- Check if {source_obj} completely covers {target_obj}
- Verify no visible gaps or misalignment
- REJECT any partial overlap or edge-touching scenarios

RESPONSE FORMAT (JSON only):
{{
  "complete_overlap_achieved": true_or_false,
  "center_distance_pixels": exact_number,
  "overlap_quality": "COMPLETE|PARTIAL|NONE",
  "action_required": "STOP_SUCCESS|CONTINUE_DRAGGING",
  "alignment_description": "detailed description of current alignment"
}}"""
                        },
                        {
                            "type": "image_url",
                            "image_url": {"url": f"data:image/png;base64,{base64_image}"}
                        }
                    ]
                }
            ],
            "temperature": 0.01  # Maximum precision
        }

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        try:
            response = requests.post(f"{self.api_url}/chat/completions", 
                                   headers=headers, json=payload, timeout=self.api_timeout)
            
            if response.status_code != 200:
                return None

            response_json = response.json()
            content = response_json["choices"][0]["message"]["content"]
            
            # Parse JSON response
            if "```json" in content:
                json_str = content.split("```json")[1].split("```")[0].strip()
            else:
                json_str = content.strip()
            
            return json.loads(json_str)

        except Exception as e:
            print(f"❌ Complete overlap verification error: {e}")
            return None

    def execute_movement(self, analysis_result):
        """OPTIMIZED movement execution with adaptive drag strategy"""
        try:
            # Parse AI response
            content = analysis_result["choices"][0]["message"]["content"]
            
            # Handle JSON parsing
            if "```json" in content:
                json_str = content.split("```json")[1].split("```")[0].strip()
            else:
                json_str = content.strip()
            
            data = json.loads(json_str)
            
            # Extract object information
            objects = {obj["name"]: obj for obj in data["objects"]}
            from_obj_name = data["move"]["from"]
            to_obj_name = data["move"]["to"]
            
            to_obj = objects[to_obj_name]
            
            # Get distance analysis from AI
            distance_analysis = data.get("distance_analysis", {})
            pixel_distance = distance_analysis.get("pixel_distance", 100)
            drag_strategy = distance_analysis.get("drag_strategy", "TWO_STEP_DRAG")
            
            print(f"🎯 ADAPTIVE DRAG STRATEGY: {drag_strategy}")
            print(f"📏 Object distance: {pixel_distance:.1f} pixels")
            
            # Calculate absolute coordinates
            captcha_left = self.captcha_bbox[0]
            start_x = self.slide_button_absolute[0]
            start_y = self.slide_button_absolute[1]
            
            # Calculate target position (NO OVERSHOOT - precision focus)
            target_x = captcha_left + to_obj["coordinates"]["x"]
            end_x = target_x  # Direct alignment, no overshoot
            end_y = start_y   # Maintain horizontal drag
            
            print(f"🎯 Target coordinates: ({end_x}, {end_y})")
            print(f"📍 Drag distance: {abs(end_x - start_x)} pixels")
            
            # Execute drag based on strategy
            if drag_strategy == "SINGLE_DRAG" or pixel_distance <= self.close_object_threshold:
                return self.execute_single_continuous_drag(start_x, start_y, end_x, end_y, from_obj_name, to_obj_name)
            else:
                return self.execute_two_step_drag(start_x, start_y, end_x, end_y, from_obj_name, to_obj_name, pixel_distance)
                
        except Exception as e:
            print(f"❌ Movement execution error: {e}")
            return False

    def execute_single_continuous_drag(self, start_x, start_y, end_x, end_y, source_obj, target_obj):
        """OPTIMIZED single continuous drag for close objects - ACCURACY PRIORITY"""
        try:
            print("🚀 SINGLE CONTINUOUS DRAG - High precision mode")

            # Move to start position
            pyautogui.moveTo(start_x, start_y, duration=0.3)
            time.sleep(0.1)

            # Start drag
            pyautogui.mouseDown(button='left')
            time.sleep(0.1)

            # Calculate precise drag duration (slower = more accurate)
            drag_distance = abs(end_x - start_x)
            drag_duration = max(2.0, min(4.0, drag_distance / 25))  # 2-4 seconds for precision

            print(f"🎯 Dragging {drag_distance}px in {drag_duration:.1f}s for maximum accuracy")

            # Execute precise drag
            pyautogui.moveTo(end_x, end_y, duration=drag_duration)

            # Release mouse
            pyautogui.mouseUp(button='left')
            time.sleep(0.5)

            # Verify complete overlap
            verification_image = self.capture_screen_area("verification_single.png")
            overlap_result = self.verify_complete_overlap(verification_image, source_obj, target_obj)

            if overlap_result and overlap_result.get("complete_overlap_achieved"):
                print("✅ SINGLE DRAG SUCCESS: Complete overlap achieved")
                return True
            else:
                print("⚠️ Single drag incomplete - may need adjustment")
                return False

        except Exception as e:
            print(f"❌ Single drag error: {e}")
            return False

    def execute_two_step_drag(self, start_x, start_y, end_x, end_y, source_obj, target_obj, pixel_distance):
        """OPTIMIZED two-step drag for far objects - COMPLETE OVERLAP FOCUS"""
        try:
            print("🎯 TWO-STEP DRAG - Complete overlap priority")

            # Move to start position
            pyautogui.moveTo(start_x, start_y, duration=0.3)
            time.sleep(0.1)

            # Start drag
            pyautogui.mouseDown(button='left')
            time.sleep(0.1)

            # Step 1: Move 60% of the way (conservative approach)
            total_distance = abs(end_x - start_x)
            step1_distance = total_distance * 0.6
            step1_x = start_x + (step1_distance if end_x > start_x else -step1_distance)

            print(f"📍 Step 1: Moving {step1_distance:.0f}px to x={step1_x:.0f}")
            pyautogui.moveTo(step1_x, end_y, duration=1.0)
            time.sleep(0.2)

            # Check overlap after step 1
            step1_image = self.capture_screen_area("step1_check.png")
            overlap_check = self.verify_complete_overlap(step1_image, source_obj, target_obj)

            if overlap_check and overlap_check.get("complete_overlap_achieved"):
                print("✅ STEP 1 SUCCESS: Complete overlap achieved early")
                pyautogui.mouseUp(button='left')
                return True

            # Step 2: Complete the remaining distance with precision
            remaining_distance = abs(end_x - step1_x)
            print(f"📍 Step 2: Moving remaining {remaining_distance:.0f}px to target")
            pyautogui.moveTo(end_x, end_y, duration=1.5)  # Slower for precision

            # Release mouse
            pyautogui.mouseUp(button='left')
            time.sleep(0.5)

            # Final verification
            final_image = self.capture_screen_area("final_check.png")
            final_overlap = self.verify_complete_overlap(final_image, source_obj, target_obj)

            if final_overlap and final_overlap.get("complete_overlap_achieved"):
                print("✅ TWO-STEP SUCCESS: Complete overlap achieved")
                return True
            else:
                print("⚠️ Two-step drag incomplete - complete overlap not achieved")
                return False

        except Exception as e:
            print(f"❌ Two-step drag error: {e}")
            return False

    def solve_captcha(self):
        """OPTIMIZED main captcha solving method - PERFORMANCE & ACCURACY PRIORITY"""
        try:
            print("🚀 STARTING OPTIMIZED CAPTCHA SOLVER")

            # Capture captcha with minimal processing
            image_path = self.capture_screen_area()
            print("📸 Screenshot captured")

            # Enhanced vision analysis
            analysis = self.analyze_with_vision(image_path)

            if analysis is None:
                print("❌ Failed to get vision analysis")
                return False

            print("🧠 Vision analysis completed")

            # Execute optimized movement
            success = self.execute_movement(analysis)

            if success:
                print("✅ CAPTCHA SOLVED SUCCESSFULLY")
            else:
                print("❌ CAPTCHA SOLVING FAILED")

            return success

        except Exception as e:
            print(f"❌ Captcha solving error: {e}")
            return False

# Usage
if __name__ == "__main__":
    time.sleep(2)  # Give user time to position cursor
    solver = OptimizedCaptchaSolver()
    #solver.capture_screen_area()
    success = solver.solve_captcha()
    print(f"🎯 Final Result: {'SUCCESS' if success else 'FAILED'}")
