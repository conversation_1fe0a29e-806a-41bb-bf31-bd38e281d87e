from typing import Any

from numpy import int_
from numpy.typing import NDArray
from numpy.polynomial._polybase import ABCPolyBase
from numpy.polynomial.polyutils import trimcoef

__all__: list[str]

legtrim = trimcoef

def poly2leg(pol): ...
def leg2poly(c): ...

legdomain: NDArray[int_]
legzero: NDArray[int_]
legone: NDArray[int_]
legx: NDArray[int_]

def legline(off, scl): ...
def legfromroots(roots): ...
def legadd(c1, c2): ...
def legsub(c1, c2): ...
def legmulx(c): ...
def legmul(c1, c2): ...
def legdiv(c1, c2): ...
def legpow(c, pow, maxpower=...): ...
def legder(c, m=..., scl=..., axis=...): ...
def legint(c, m=..., k = ..., lbnd=..., scl=..., axis=...): ...
def legval(x, c, tensor=...): ...
def legval2d(x, y, c): ...
def leggrid2d(x, y, c): ...
def legval3d(x, y, z, c): ...
def leggrid3d(x, y, z, c): ...
def legvander(x, deg): ...
def legvander2d(x, y, deg): ...
def legvander3d(x, y, z, deg): ...
def legfit(x, y, deg, rcond=..., full=..., w=...): ...
def legcompanion(c): ...
def legroots(c): ...
def leggauss(deg): ...
def legweight(x): ...

class Legendre(ABCPolyBase):
    domain: Any
    window: Any
    basis_name: Any
