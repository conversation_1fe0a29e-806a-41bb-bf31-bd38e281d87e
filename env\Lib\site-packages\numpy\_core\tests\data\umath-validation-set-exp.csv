dtype,input,output,ulperrortol
## +ve denormals ##
np.float32,0x004b4716,0x3f800000,3
np.float32,0x007b2490,0x3f800000,3
np.float32,0x007c99fa,0x3f800000,3
np.float32,0x00734a0c,0x3f800000,3
np.float32,0x0070de24,0x3f800000,3
np.float32,0x00495d65,0x3f800000,3
np.float32,0x006894f6,0x3f800000,3
np.float32,0x00555a76,0x3f800000,3
np.float32,0x004e1fb8,0x3f800000,3
np.float32,0x00687de9,0x3f800000,3
## -ve denormals ##
np.float32,0x805b59af,0x3f800000,3
np.float32,0x807ed8ed,0x3f800000,3
np.float32,0x807142ad,0x3f800000,3
np.float32,0x80772002,0x3f800000,3
np.float32,0x8062abcb,0x3f800000,3
np.float32,0x8045e31c,0x3f800000,3
np.float32,0x805f01c2,0x3f800000,3
np.float32,0x80506432,0x3f800000,3
np.float32,0x8060089d,0x3f800000,3
np.float32,0x8071292f,0x3f800000,3
## floats that output a denormal ##
np.float32,0xc2cf3fc1,0x00000001,3
np.float32,0xc2c79726,0x00000021,3
np.float32,0xc2cb295d,0x00000005,3
np.float32,0xc2b49e6b,0x00068c4c,3
np.float32,0xc2ca8116,0x00000008,3
np.float32,0xc2c23f82,0x000001d7,3
np.float32,0xc2cb69c0,0x00000005,3
np.float32,0xc2cc1f4d,0x00000003,3
np.float32,0xc2ae094e,0x00affc4c,3
np.float32,0xc2c86c44,0x00000015,3
## random floats between -87.0f and 88.0f ##
np.float32,0x4030d7e0,0x417d9a05,3
np.float32,0x426f60e8,0x6aa1be2c,3
np.float32,0x41a1b220,0x4e0efc11,3
np.float32,0xc20cc722,0x26159da7,3
np.float32,0x41c492bc,0x512ec79d,3
np.float32,0x40980210,0x42e73a0e,3
np.float32,0xbf1f7b80,0x3f094de3,3
np.float32,0x42a678a4,0x7b87a383,3
np.float32,0xc20f3cfd,0x25a1c304,3
np.float32,0x423ff34c,0x6216467f,3
np.float32,0x00000000,0x3f800000,3
## floats that cause an overflow ##
np.float32,0x7f06d8c1,0x7f800000,3
np.float32,0x7f451912,0x7f800000,3
np.float32,0x7ecceac3,0x7f800000,3
np.float32,0x7f643b45,0x7f800000,3
np.float32,0x7e910ea0,0x7f800000,3
np.float32,0x7eb4756b,0x7f800000,3
np.float32,0x7f4ec708,0x7f800000,3
np.float32,0x7f6b4551,0x7f800000,3
np.float32,0x7d8edbda,0x7f800000,3
np.float32,0x7f730718,0x7f800000,3
np.float32,0x42b17217,0x7f7fff84,3
np.float32,0x42b17218,0x7f800000,3
np.float32,0x42b17219,0x7f800000,3
np.float32,0xfef2b0bc,0x00000000,3
np.float32,0xff69f83e,0x00000000,3
np.float32,0xff4ecb12,0x00000000,3
np.float32,0xfeac6d86,0x00000000,3
np.float32,0xfde0cdb8,0x00000000,3
np.float32,0xff26aef4,0x00000000,3
np.float32,0xff6f9277,0x00000000,3
np.float32,0xff7adfc4,0x00000000,3
np.float32,0xff0ad40e,0x00000000,3
np.float32,0xff6fd8f3,0x00000000,3
np.float32,0xc2cff1b4,0x00000001,3
np.float32,0xc2cff1b5,0x00000000,3
np.float32,0xc2cff1b6,0x00000000,3
np.float32,0x7f800000,0x7f800000,3
np.float32,0xff800000,0x00000000,3
np.float32,0x4292f27c,0x7480000a,3
np.float32,0x42a920be,0x7c7fff94,3
np.float32,0x41c214c9,0x50ffffd9,3
np.float32,0x41abe686,0x4effffd9,3
np.float32,0x4287db5a,0x707fffd3,3
np.float32,0x41902cbb,0x4c800078,3
np.float32,0x42609466,0x67ffffeb,3
np.float32,0x41a65af5,0x4e7fffd1,3
np.float32,0x417f13ff,0x4affffc9,3
np.float32,0x426d0e6c,0x6a3504f2,3
np.float32,0x41bc8934,0x507fff51,3
np.float32,0x42a7bdde,0x7c0000d6,3
np.float32,0x4120cf66,0x46b504f6,3
np.float32,0x4244da8f,0x62ffff1a,3
np.float32,0x41a0cf69,0x4e000034,3
np.float32,0x41cd2bec,0x52000005,3
np.float32,0x42893e41,0x7100009e,3
np.float32,0x41b437e1,0x4fb50502,3
np.float32,0x41d8430f,0x5300001d,3
np.float32,0x4244da92,0x62ffffda,3
np.float32,0x41a0cf63,0x4dffffa9,3
np.float32,0x3eb17218,0x3fb504f3,3
np.float32,0x428729e8,0x703504dc,3
np.float32,0x41a0cf67,0x4e000014,3
np.float32,0x4252b77d,0x65800011,3
np.float32,0x41902cb9,0x4c800058,3
np.float32,0x42a0cf67,0x79800052,3
np.float32,0x4152b77b,0x48ffffe9,3
np.float32,0x41265af3,0x46ffffc8,3
np.float32,0x42187e0b,0x5affff9a,3
np.float32,0xc0d2b77c,0x3ab504f6,3
np.float32,0xc283b2ac,0x10000072,3
np.float32,0xc1cff1b4,0x2cb504f5,3
np.float32,0xc05dce9e,0x3d000000,3
np.float32,0xc28ec9d2,0x0bfffea5,3
np.float32,0xc23c893a,0x1d7fffde,3
np.float32,0xc2a920c0,0x027fff6c,3
np.float32,0xc1f9886f,0x2900002b,3
np.float32,0xc2c42920,0x000000b5,3
np.float32,0xc2893e41,0x0dfffec5,3
np.float32,0xc2c4da93,0x00000080,3
np.float32,0xc17f1401,0x3400000c,3
np.float32,0xc1902cb6,0x327fffaf,3
np.float32,0xc27c4e3b,0x11ffffc5,3
np.float32,0xc268e5c5,0x157ffe9d,3
np.float32,0xc2b4e953,0x0005a826,3
np.float32,0xc287db5a,0x0e800016,3
np.float32,0xc207db5a,0x2700000b,3
np.float32,0xc2b2d4fe,0x000ffff1,3
np.float32,0xc268e5c0,0x157fffdd,3
np.float32,0xc22920bd,0x2100003b,3
np.float32,0xc2902caf,0x0b80011e,3
np.float32,0xc1902cba,0x327fff2f,3
np.float32,0xc2ca6625,0x00000008,3
np.float32,0xc280ece8,0x10fffeb5,3
np.float32,0xc2918f94,0x0b0000ea,3
np.float32,0xc29b43d5,0x077ffffc,3
np.float32,0xc1e61ff7,0x2ab504f5,3
np.float32,0xc2867878,0x0effff15,3
np.float32,0xc2a2324a,0x04fffff4,3
#float64
## near zero ##
np.float64,0x8000000000000000,0x3ff0000000000000,1
np.float64,0x8010000000000000,0x3ff0000000000000,1
np.float64,0x8000000000000001,0x3ff0000000000000,1
np.float64,0x8360000000000000,0x3ff0000000000000,1
np.float64,0x9a70000000000000,0x3ff0000000000000,1
np.float64,0xb9b0000000000000,0x3ff0000000000000,1
np.float64,0xb810000000000000,0x3ff0000000000000,1
np.float64,0xbc30000000000000,0x3ff0000000000000,1
np.float64,0xb6a0000000000000,0x3ff0000000000000,1
np.float64,0x0000000000000000,0x3ff0000000000000,1
np.float64,0x0010000000000000,0x3ff0000000000000,1
np.float64,0x0000000000000001,0x3ff0000000000000,1
np.float64,0x0360000000000000,0x3ff0000000000000,1
np.float64,0x1a70000000000000,0x3ff0000000000000,1
np.float64,0x3c30000000000000,0x3ff0000000000000,1
np.float64,0x36a0000000000000,0x3ff0000000000000,1
np.float64,0x39b0000000000000,0x3ff0000000000000,1
np.float64,0x3810000000000000,0x3ff0000000000000,1
## underflow ##
np.float64,0xc0c6276800000000,0x0000000000000000,1
np.float64,0xc0c62d918ce2421d,0x0000000000000000,1
np.float64,0xc0c62d918ce2421e,0x0000000000000000,1
np.float64,0xc0c62d91a0000000,0x0000000000000000,1
np.float64,0xc0c62d9180000000,0x0000000000000000,1
np.float64,0xc0c62dea45ee3e06,0x0000000000000000,1
np.float64,0xc0c62dea45ee3e07,0x0000000000000000,1
np.float64,0xc0c62dea40000000,0x0000000000000000,1
np.float64,0xc0c62dea60000000,0x0000000000000000,1
np.float64,0xc0875f1120000000,0x0000000000000000,1
np.float64,0xc0875f113c30b1c8,0x0000000000000000,1
np.float64,0xc0875f1140000000,0x0000000000000000,1
np.float64,0xc093480000000000,0x0000000000000000,1
np.float64,0xffefffffffffffff,0x0000000000000000,1
np.float64,0xc7efffffe0000000,0x0000000000000000,1
## overflow ##
np.float64,0x40862e52fefa39ef,0x7ff0000000000000,1
np.float64,0x40872e42fefa39ef,0x7ff0000000000000,1
## +/- INF, +/- NAN ##
np.float64,0x7ff0000000000000,0x7ff0000000000000,1
np.float64,0xfff0000000000000,0x0000000000000000,1
np.float64,0x7ff8000000000000,0x7ff8000000000000,1
np.float64,0xfff8000000000000,0xfff8000000000000,1
## output denormal ##
np.float64,0xc087438520000000,0x0000000000000001,1
np.float64,0xc08743853f2f4461,0x0000000000000001,1
np.float64,0xc08743853f2f4460,0x0000000000000001,1
np.float64,0xc087438540000000,0x0000000000000001,1
## between -745.13321910 and 709.78271289 ##
np.float64,0xbff760cd14774bd9,0x3fcdb14ced00ceb6,1
np.float64,0xbff760cd20000000,0x3fcdb14cd7993879,1
np.float64,0xbff760cd00000000,0x3fcdb14d12fbd264,1
np.float64,0xc07f1cf360000000,0x130c1b369af14fda,1
np.float64,0xbeb0000000000000,0x3feffffe00001000,1
np.float64,0xbd70000000000000,0x3fefffffffffe000,1
np.float64,0xc084fd46e5c84952,0x0360000000000139,1
np.float64,0xc084fd46e5c84953,0x035ffffffffffe71,1
np.float64,0xc084fd46e0000000,0x0360000b9096d32c,1
np.float64,0xc084fd4700000000,0x035fff9721d12104,1
np.float64,0xc086232bc0000000,0x0010003af5e64635,1
np.float64,0xc086232bdd7abcd2,0x001000000000007c,1
np.float64,0xc086232bdd7abcd3,0x000ffffffffffe7c,1
np.float64,0xc086232be0000000,0x000ffffaf57a6fc9,1
np.float64,0xc086233920000000,0x000fe590e3b45eb0,1
np.float64,0xc086233938000000,0x000fe56133493c57,1
np.float64,0xc086233940000000,0x000fe5514deffbbc,1
np.float64,0xc086234c98000000,0x000fbf1024c32ccb,1
np.float64,0xc086234ca0000000,0x000fbf0065bae78d,1
np.float64,0xc086234c80000000,0x000fbf3f623a7724,1
np.float64,0xc086234ec0000000,0x000fbad237c846f9,1
np.float64,0xc086234ec8000000,0x000fbac27cfdec97,1
np.float64,0xc086234ee0000000,0x000fba934cfd3dc2,1
np.float64,0xc086234ef0000000,0x000fba73d7f618d9,1
np.float64,0xc086234f00000000,0x000fba54632dddc0,1
np.float64,0xc0862356e0000000,0x000faae0945b761a,1
np.float64,0xc0862356f0000000,0x000faac13eb9a310,1
np.float64,0xc086235700000000,0x000faaa1e9567b0a,1
np.float64,0xc086236020000000,0x000f98cd75c11ed7,1
np.float64,0xc086236ca0000000,0x000f8081b4d93f89,1
np.float64,0xc086236cb0000000,0x000f8062b3f4d6c5,1
np.float64,0xc086236cc0000000,0x000f8043b34e6f8c,1
np.float64,0xc086238d98000000,0x000f41220d9b0d2c,1
np.float64,0xc086238da0000000,0x000f4112cc80a01f,1
np.float64,0xc086238d80000000,0x000f414fd145db5b,1
np.float64,0xc08624fd00000000,0x000cbfce8ea1e6c4,1
np.float64,0xc086256080000000,0x000c250747fcd46e,1
np.float64,0xc08626c480000000,0x000a34f4bd975193,1
np.float64,0xbf50000000000000,0x3feff800ffeaac00,1
np.float64,0xbe10000000000000,0x3fefffffff800000,1
np.float64,0xbcd0000000000000,0x3feffffffffffff8,1
np.float64,0xc055d589e0000000,0x38100004bf94f63e,1
np.float64,0xc055d58a00000000,0x380ffff97f292ce8,1
np.float64,0xbfd962d900000000,0x3fe585a4b00110e1,1
np.float64,0x3ff4bed280000000,0x400d411e7a58a303,1
np.float64,0x3fff0b3620000000,0x401bd7737ffffcf3,1
np.float64,0x3ff0000000000000,0x4005bf0a8b145769,1
np.float64,0x3eb0000000000000,0x3ff0000100000800,1
np.float64,0x3d70000000000000,0x3ff0000000001000,1
np.float64,0x40862e42e0000000,0x7fefff841808287f,1
np.float64,0x40862e42fefa39ef,0x7fefffffffffff2a,1
np.float64,0x40862e0000000000,0x7feef85a11e73f2d,1
np.float64,0x4000000000000000,0x401d8e64b8d4ddae,1
np.float64,0x4009242920000000,0x40372a52c383a488,1
np.float64,0x4049000000000000,0x44719103e4080b45,1
np.float64,0x4008000000000000,0x403415e5bf6fb106,1
np.float64,0x3f50000000000000,0x3ff00400800aab55,1
np.float64,0x3e10000000000000,0x3ff0000000400000,1
np.float64,0x3cd0000000000000,0x3ff0000000000004,1
np.float64,0x40562e40a0000000,0x47effed088821c3f,1
np.float64,0x40562e42e0000000,0x47effff082e6c7ff,1
np.float64,0x40562e4300000000,0x47f00000417184b8,1
np.float64,0x3fe8000000000000,0x4000ef9db467dcf8,1
np.float64,0x402b12e8d4f33589,0x412718f68c71a6fe,1
np.float64,0x402b12e8d4f3358a,0x412718f68c71a70a,1
np.float64,0x402b12e8c0000000,0x412718f59a7f472e,1
np.float64,0x402b12e8e0000000,0x412718f70c0eac62,1
##use 1th entry
np.float64,0x40631659AE147CB4,0x4db3a95025a4890f,1
np.float64,0xC061B87D2E85A4E2,0x332640c8e2de2c51,1
np.float64,0x405A4A50BE243AF4,0x496a45e4b7f0339a,1
np.float64,0xC0839898B98EC5C6,0x0764027828830df4,1
#use 2th entry
np.float64,0xC072428C44B6537C,0x2596ade838b96f3e,1
np.float64,0xC053057C5E1AE9BF,0x3912c8fad18fdadf,1
np.float64,0x407E89C78328BAA3,0x6bfe35d5b9a1a194,1
np.float64,0x4083501B6DD87112,0x77a855503a38924e,1
#use 3th entry
np.float64,0x40832C6195F24540,0x7741e73c80e5eb2f,1
np.float64,0xC083D4CD557C2EC9,0x06b61727c2d2508e,1
np.float64,0x400C48F5F67C99BD,0x404128820f02b92e,1
np.float64,0x4056E36D9B2DF26A,0x4830f52ff34a8242,1
#use 4th entry
np.float64,0x4080FF700D8CBD06,0x70fa70df9bc30f20,1
np.float64,0x406C276D39E53328,0x543eb8e20a8f4741,1
np.float64,0xC070D6159BBD8716,0x27a4a0548c904a75,1
np.float64,0xC052EBCF8ED61F83,0x391c0e92368d15e4,1
#use 5th entry
np.float64,0xC061F892A8AC5FBE,0x32f807a89efd3869,1
np.float64,0x4021D885D2DBA085,0x40bd4dc86d3e3270,1
np.float64,0x40767AEEEE7D4FCF,0x605e22851ee2afb7,1
np.float64,0xC0757C5D75D08C80,0x20f0751599b992a2,1
#use 6th entry
np.float64,0x405ACF7A284C4CE3,0x499a4e0b7a27027c,1
np.float64,0xC085A6C9E80D7AF5,0x0175914009d62ec2,1
np.float64,0xC07E4C02F86F1DAE,0x1439269b29a9231e,1
np.float64,0x4080D80F9691CC87,0x7088a6cdafb041de,1
#use 7th entry
np.float64,0x407FDFD84FBA0AC1,0x6deb1ae6f9bc4767,1
np.float64,0x40630C06A1A2213D,0x4dac7a9d51a838b7,1
np.float64,0x40685FDB30BB8B4F,0x5183f5cc2cac9e79,1
np.float64,0x408045A2208F77F4,0x6ee299e08e2aa2f0,1
#use 8th entry
np.float64,0xC08104E391F5078B,0x0ed397b7cbfbd230,1
np.float64,0xC031501CAEFAE395,0x3e6040fd1ea35085,1
np.float64,0xC079229124F6247C,0x1babf4f923306b1e,1
np.float64,0x407FB65F44600435,0x6db03beaf2512b8a,1
#use 9th entry
np.float64,0xC07EDEE8E8E8A5AC,0x136536cec9cbef48,1
np.float64,0x4072BB4086099A14,0x5af4d3c3008b56cc,1
np.float64,0x4050442A2EC42CB4,0x45cd393bd8fad357,1
np.float64,0xC06AC28FB3D419B4,0x2ca1b9d3437df85f,1
#use 10th entry
np.float64,0x40567FC6F0A68076,0x480c977fd5f3122e,1
np.float64,0x40620A2F7EDA59BB,0x4cf278e96f4ce4d7,1
np.float64,0xC085044707CD557C,0x034aad6c968a045a,1
np.float64,0xC07374EA5AC516AA,0x23dd6afdc03e83d5,1
#use 11th entry
np.float64,0x4073CC95332619C1,0x5c804b1498bbaa54,1
np.float64,0xC0799FEBBE257F31,0x1af6a954c43b87d2,1
np.float64,0x408159F19EA424F6,0x7200858efcbfc84d,1
np.float64,0x404A81F6F24C0792,0x44b664a07ce5bbfa,1
#use 12th entry
np.float64,0x40295FF1EFB9A741,0x4113c0e74c52d7b0,1
np.float64,0x4073975F4CC411DA,0x5c32be40b4fec2c1,1
np.float64,0x406E9DE52E82A77E,0x56049c9a3f1ae089,1
np.float64,0x40748C2F52560ED9,0x5d93bc14fd4cd23b,1
#use 13th entry
np.float64,0x4062A553CDC4D04C,0x4d6266bfde301318,1
np.float64,0xC079EC1D63598AB7,0x1a88cb184dab224c,1
np.float64,0xC0725C1CB3167427,0x25725b46f8a081f6,1
np.float64,0x407888771D9B45F9,0x6353b1ec6bd7ce80,1
#use 14th entry
np.float64,0xC082CBA03AA89807,0x09b383723831ce56,1
np.float64,0xC083A8961BB67DD7,0x0735b118d5275552,1
np.float64,0xC076BC6ECA12E7E3,0x1f2222679eaef615,1
np.float64,0xC072752503AA1A5B,0x254eb832242c77e1,1
#use 15th entry
np.float64,0xC058800792125DEC,0x371882372a0b48d4,1
np.float64,0x4082909FD863E81C,0x7580d5f386920142,1
np.float64,0xC071616F8FB534F9,0x26dbe20ef64a412b,1
np.float64,0x406D1AB571CAA747,0x54ee0d55cb38ac20,1
#use 16th entry
np.float64,0x406956428B7DAD09,0x52358682c271237f,1
np.float64,0xC07EFC2D9D17B621,0x133b3e77c27a4d45,1
np.float64,0xC08469BAC5BA3CCA,0x050863e5f42cc52f,1
np.float64,0x407189D9626386A5,0x593cb1c0b3b5c1d3,1
#use 17th entry
np.float64,0x4077E652E3DEB8C6,0x6269a10dcbd3c752,1
np.float64,0x407674C97DB06878,0x605485dcc2426ec2,1
np.float64,0xC07CE9969CF4268D,0x16386cf8996669f2,1
np.float64,0x40780EE32D5847C4,0x62a436bd1abe108d,1
#use 18th entry
np.float64,0x4076C3AA5E1E8DA1,0x60c62f56a5e72e24,1
np.float64,0xC0730AFC7239B9BE,0x24758ead095cec1e,1
np.float64,0xC085CC2B9C420DDB,0x0109cdaa2e5694c1,1
np.float64,0x406D0765CB6D7AA4,0x54e06f8dd91bd945,1
#use 19th entry
np.float64,0xC082D011F3B495E7,0x09a6647661d279c2,1
np.float64,0xC072826AF8F6AFBC,0x253acd3cd224507e,1
np.float64,0x404EB9C4810CEA09,0x457933dbf07e8133,1
np.float64,0x408284FBC97C58CE,0x755f6eb234aa4b98,1
#use 20th entry
np.float64,0x40856008CF6EDC63,0x7d9c0b3c03f4f73c,1
np.float64,0xC077CB2E9F013B17,0x1d9b3d3a166a55db,1
np.float64,0xC0479CA3C20AD057,0x3bad40e081555b99,1
np.float64,0x40844CD31107332A,0x7a821d70aea478e2,1
#use 21th entry
np.float64,0xC07C8FCC0BFCC844,0x16ba1cc8c539d19b,1
np.float64,0xC085C4E9A3ABA488,0x011ff675ba1a2217,1
np.float64,0x4074D538B32966E5,0x5dfd9d78043c6ad9,1
np.float64,0xC0630CA16902AD46,0x3231a446074cede6,1
#use 22th entry
np.float64,0xC06C826733D7D0B7,0x2b5f1078314d41e1,1
np.float64,0xC0520DF55B2B907F,0x396c13a6ce8e833e,1
np.float64,0xC080712072B0F437,0x107eae02d11d98ea,1
np.float64,0x40528A6150E19EFB,0x469fdabda02228c5,1
#use 23th entry
np.float64,0xC07B1D74B6586451,0x18d1253883ae3b48,1
np.float64,0x4045AFD7867DAEC0,0x43d7d634fc4c5d98,1
np.float64,0xC07A08B91F9ED3E2,0x1a60973e6397fc37,1
np.float64,0x407B3ECF0AE21C8C,0x673e03e9d98d7235,1
#use 24th entry
np.float64,0xC078AEB6F30CEABF,0x1c530b93ab54a1b3,1
np.float64,0x4084495006A41672,0x7a775b6dc7e63064,1
np.float64,0x40830B1C0EBF95DD,0x76e1e6eed77cfb89,1
np.float64,0x407D93E8F33D8470,0x6a9adbc9e1e4f1e5,1
#use 25th entry
np.float64,0x4066B11A09EFD9E8,0x504dd528065c28a7,1
np.float64,0x408545823723AEEB,0x7d504a9b1844f594,1
np.float64,0xC068C711F2CA3362,0x2e104f3496ea118e,1
np.float64,0x407F317FCC3CA873,0x6cf0732c9948ebf4,1
#use 26th entry
np.float64,0x407AFB3EBA2ED50F,0x66dc28a129c868d5,1
np.float64,0xC075377037708ADE,0x21531a329f3d793e,1
np.float64,0xC07C30066A1F3246,0x174448baa16ded2b,1
np.float64,0xC06689A75DE2ABD3,0x2fad70662fae230b,1
#use 27th entry
np.float64,0x4081514E9FCCF1E0,0x71e673b9efd15f44,1
np.float64,0xC0762C710AF68460,0x1ff1ed7d8947fe43,1
np.float64,0xC0468102FF70D9C4,0x3be0c3a8ff3419a3,1
np.float64,0xC07EA4CEEF02A83E,0x13b908f085102c61,1
#use 28th entry
np.float64,0xC06290B04AE823C4,0x328a83da3c2e3351,1
np.float64,0xC0770EB1D1C395FB,0x1eab281c1f1db5fe,1
np.float64,0xC06F5D4D838A5BAE,0x29500ea32fb474ea,1
np.float64,0x40723B3133B54C5D,0x5a3c82c7c3a2b848,1
#use 29th entry
np.float64,0x4085E6454CE3B4AA,0x7f20319b9638d06a,1
np.float64,0x408389F2A0585D4B,0x7850667c58aab3d0,1
np.float64,0xC0382798F9C8AE69,0x3dc1c79fe8739d6d,1
np.float64,0xC08299D827608418,0x0a4335f76cdbaeb5,1
#use 30th entry
np.float64,0xC06F3DED43301BF1,0x2965670ae46750a8,1
np.float64,0xC070CAF6BDD577D9,0x27b4aa4ffdd29981,1
np.float64,0x4078529AD4B2D9F2,0x6305c12755d5e0a6,1
np.float64,0xC055B14E75A31B96,0x381c2eda6d111e5d,1
#use 31th entry
np.float64,0x407B13EE414FA931,0x6700772c7544564d,1
np.float64,0x407EAFDE9DE3EC54,0x6c346a0e49724a3c,1
np.float64,0xC08362F398B9530D,0x07ffeddbadf980cb,1
np.float64,0x407E865CDD9EEB86,0x6bf866cac5e0d126,1
#use 32th entry
np.float64,0x407FB62DBC794C86,0x6db009f708ac62cb,1
np.float64,0xC063D0BAA68CDDDE,0x31a3b2a51ce50430,1
np.float64,0xC05E7706A2231394,0x34f24bead6fab5c9,1
np.float64,0x4083E3A06FDE444E,0x79527b7a386d1937,1
